// 🔧 Smart Connections API 自动诊断脚本
// 在Obsidian控制台中运行此脚本 (Ctrl+Shift+I)

console.log("🔍 开始 Smart Connections API 诊断...");
console.log("=".repeat(50));

// 1. 检查Smart Connections插件
const scPlugin = app.plugins.plugins["smart-connections"];
console.log("1. Smart Connections插件状态:");
console.log("   插件存在:", !!scPlugin);
console.log("   插件已启用:", scPlugin?.enabled);
console.log("   插件对象:", scPlugin);

// 2. 检查v3.0 API
console.log("\n2. Smart Connections v3.0 API检查:");
console.log("   window.smart_env:", !!window.smart_env);
console.log("   smart_env.state:", window.smart_env?.state);
console.log("   smart_sources:", !!window.smart_env?.smart_sources);
console.log("   lookup方法:", !!window.smart_env?.smart_sources?.lookup);

if (window.smart_env?.smart_sources?.lookup) {
    console.log("   ✅ v3.0 API 可用!");
} else {
    console.log("   ❌ v3.0 API 不可用");
}

// 3. 检查v2.x API
console.log("\n3. Smart Connections v2.x API检查:");
console.log("   window.SmartSearch:", !!window.SmartSearch);
console.log("   插件env属性:", !!scPlugin?.env);

if (window.SmartSearch) {
    console.log("   ✅ v2.x API (window.SmartSearch) 可用!");
    console.log("   API方法:", Object.keys(window.SmartSearch));
} else if (scPlugin?.env) {
    console.log("   ⚠️  插件env存在，尝试设置API...");
    window.SmartSearch = scPlugin.env;
    console.log("   ✅ API已手动设置!");
} else {
    console.log("   ❌ v2.x API 不可用");
}

// 4. 检查MCP Tools插件
console.log("\n4. MCP Tools插件状态:");
const mcpPlugin = app.plugins.plugins["mcp-tools"];
console.log("   MCP Tools存在:", !!mcpPlugin);
console.log("   MCP Tools已启用:", mcpPlugin?.enabled);

// 5. 尝试修复
console.log("\n5. 尝试自动修复:");

if (!window.SmartSearch && scPlugin?.env) {
    console.log("   🔧 尝试手动设置API...");
    window.SmartSearch = scPlugin.env;
    console.log("   ✅ API已设置:", !!window.SmartSearch);
}

// 6. 最终状态
console.log("\n6. 最终诊断结果:");
const hasV3API = window.smart_env?.smart_sources?.lookup && window.smart_env?.state === 'loaded';
const hasV2API = !!window.SmartSearch;

if (hasV3API) {
    console.log("   🎉 Smart Connections v3.0 API 可用!");
} else if (hasV2API) {
    console.log("   🎉 Smart Connections v2.x API 可用!");
} else {
    console.log("   ❌ Smart Connections API 不可用");
    console.log("\n🛠️  建议修复步骤:");
    console.log("   1. 禁用Smart Connections插件");
    console.log("   2. 重启Obsidian");
    console.log("   3. 重新启用Smart Connections插件");
    console.log("   4. 等待插件完全加载");
    console.log("   5. 重新运行此诊断脚本");
}

console.log("\n" + "=".repeat(50));
console.log("🔍 诊断完成!");

// 7. 如果API可用，尝试测试搜索
if (hasV3API || hasV2API) {
    console.log("\n🧪 尝试测试搜索功能...");
    
    if (hasV3API) {
        // 测试v3.0 API
        try {
            window.smart_env.smart_sources.lookup({ hypotheticals: ["test"] })
                .then(results => {
                    console.log("   ✅ v3.0 搜索测试成功:", results.length, "个结果");
                })
                .catch(error => {
                    console.log("   ❌ v3.0 搜索测试失败:", error);
                });
        } catch (error) {
            console.log("   ❌ v3.0 搜索测试错误:", error);
        }
    } else if (hasV2API && window.SmartSearch.search) {
        // 测试v2.x API
        try {
            window.SmartSearch.search("test", {limit: 1})
                .then(results => {
                    console.log("   ✅ v2.x 搜索测试成功:", results.length, "个结果");
                })
                .catch(error => {
                    console.log("   ❌ v2.x 搜索测试失败:", error);
                });
        } catch (error) {
            console.log("   ❌ v2.x 搜索测试错误:", error);
        }
    }
}
