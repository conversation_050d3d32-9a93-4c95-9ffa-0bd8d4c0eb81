# 🔧 Smart Connections API 调试指南

## 🎯 问题确诊

`search_vault_smart` 404错误的根本原因：**Smart Connections插件的API没有正确暴露**

## 🧪 诊断步骤

### 步骤1: 检查Smart Connections插件状态

在Obsidian中打开控制台（Ctrl+Shift+I），运行：

```javascript
// 检查插件是否安装
console.log("Smart Connections Plugin:", app.plugins.plugins["smart-connections"]);

// 检查v3.0 API
console.log("window.smart_env:", window.smart_env);
console.log("smart_env.state:", window.smart_env?.state);
console.log("smart_sources:", window.smart_env?.smart_sources);

// 检查v2.x API  
console.log("window.SmartSearch:", window.SmartSearch);

// 检查插件env
const scPlugin = app.plugins.plugins["smart-connections"];
console.log("Plugin env:", scPlugin?.env);
```

### 步骤2: 强制重新加载Smart Connections

1. **禁用Smart Connections插件**
2. **重启Obsidian** 
3. **重新启用Smart Connections插件**
4. **等待插件完全加载**（查看状态栏或通知）

### 步骤3: 验证API加载

再次运行上面的诊断代码，应该看到：
```javascript
// 期望结果之一：
window.smart_env.state: "loaded"
// 或者：
window.SmartSearch: [Object object]
```

### 步骤4: 如果还不工作

尝试这些修复：

```javascript
// 在Obsidian控制台中强制设置API
const scPlugin = app.plugins.plugins["smart-connections"];
if (scPlugin && scPlugin.env) {
    window.SmartSearch = scPlugin.env;
    console.log("API manually set:", window.SmartSearch);
}
```

## 🔧 常见问题解决

### 问题1: Smart Connections未安装
**解决**：社区插件 → 搜索"Smart Connections" → 安装

### 问题2: 插件已安装但API未加载
**解决**：
1. 禁用插件 → 重启Obsidian → 重新启用
2. 检查插件是否有错误日志

### 问题3: API检测超时
**解决**：
```javascript
// 手动触发API检测
const mcpPlugin = app.plugins.plugins["mcp-tools"];
if (mcpPlugin) {
    console.log("MCP Tools plugin found");
    // 重新加载API检测
}
```

## 📊 成功标志

修复成功后，应该看到：
```javascript
obsidian-mcp-tools:search_vault_smart({
  query: "钢球制造工艺",
  filter: {limit: 5}
})
// 返回语义搜索结果，而不是404错误
```

## 🚨 如果仍不工作

如果上述步骤都尝试了还不工作，可能需要：

1. **重新安装Smart Connections插件**
2. **检查Smart Connections版本兼容性**
3. **使用替代搜索方案**：
   ```javascript
   // 高质量的关键词搜索
   search_vault_simple({query: "钢球 制造 工艺 技术"})
   ```

**立即尝试第一步：检查插件状态！**
