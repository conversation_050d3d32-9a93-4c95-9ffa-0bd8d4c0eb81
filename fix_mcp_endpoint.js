// 🔧 修复MCP Tools插件端点注册
// 在Obsidian控制台中运行

console.log("🔧 开始修复MCP Tools端点注册...");

// 1. 检查当前注册的端点
console.log("\n1. 检查Local REST API端点:");
const localRestPlugin = app.plugins.plugins["obsidian-local-rest-api"];
if (localRestPlugin && localRestPlugin.api) {
    console.log("   Local REST API存在:", !!localRestPlugin.api);
    
    // 尝试获取注册的路由
    if (localRestPlugin.api.router) {
        console.log("   路由器存在:", !!localRestPlugin.api.router);
        console.log("   路由栈:", localRestPlugin.api.router.stack?.length || 0, "个路由");
        
        // 查找/search/smart路由
        const searchRoutes = localRestPlugin.api.router.stack?.filter(layer => 
            layer.route && layer.route.path && layer.route.path.includes('search')
        );
        console.log("   搜索相关路由:", searchRoutes?.length || 0);
        
        searchRoutes?.forEach(route => {
            console.log("     -", route.route.path, Object.keys(route.route.methods));
        });
    }
}

// 2. 检查MCP Tools插件状态
console.log("\n2. 检查MCP Tools插件:");
const mcpPlugin = app.plugins.plugins["mcp-tools"];
console.log("   MCP Tools存在:", !!mcpPlugin);
console.log("   MCP Tools已加载:", mcpPlugin?._loaded);

// 3. 尝试重启MCP Tools插件
console.log("\n3. 尝试重启MCP Tools插件:");
if (mcpPlugin) {
    try {
        console.log("   正在卸载MCP Tools...");
        await mcpPlugin.unload();
        console.log("   ✅ MCP Tools已卸载");
        
        console.log("   正在重新加载MCP Tools...");
        await mcpPlugin.load();
        console.log("   ✅ MCP Tools已重新加载");
        
        // 等待一下让端点注册完成
        setTimeout(() => {
            console.log("\n4. 重新检查端点注册:");
            const searchRoutes = localRestPlugin.api?.router?.stack?.filter(layer => 
                layer.route && layer.route.path && layer.route.path.includes('search')
            );
            console.log("   搜索相关路由:", searchRoutes?.length || 0);
            
            searchRoutes?.forEach(route => {
                console.log("     -", route.route.path, Object.keys(route.route.methods));
            });
            
            console.log("\n🎯 修复完成！请现在测试search_vault_smart");
        }, 2000);
        
    } catch (error) {
        console.error("   ❌ 重启MCP Tools失败:", error);
        
        // 尝试替代方案：手动禁用/启用
        console.log("\n   尝试替代方案: 手动禁用/启用");
        app.plugins.disablePlugin("mcp-tools")
            .then(() => {
                console.log("   ✅ MCP Tools已禁用");
                return new Promise(resolve => setTimeout(resolve, 1000));
            })
            .then(() => {
                console.log("   正在重新启用...");
                return app.plugins.enablePlugin("mcp-tools");
            })
            .then(() => {
                console.log("   ✅ MCP Tools已重新启用");
                console.log("\n🎯 修复完成！请现在测试search_vault_smart");
            })
            .catch(err => {
                console.error("   ❌ 手动重启也失败:", err);
                console.log("\n🔧 建议手动操作:");
                console.log("   1. 在设置中禁用MCP Tools插件");
                console.log("   2. 重启Obsidian");
                console.log("   3. 重新启用MCP Tools插件");
            });
    }
} else {
    console.log("   ❌ 未找到MCP Tools插件");
}

console.log("\n" + "=".repeat(50));
